---
type: "always_apply"
---

# 加密货币量化交易系统开发规则

## 🌐 语言和编码规范
- **项目语言**: 所有代码、注释、文档使用中文
- **编码标准**: UTF-8编码，PEP8代码规范（中文注释）
- **文档语言**: 详细的中文文档和注释
- **代码质量控制**: 严格遵循PEP8代码规范，所有函数必须包含中文文档字符串
- **单元测试覆盖率**: 90%以上的测试覆盖率要求

## 📊 数据质量和真实性要求（严格执行）
### 数据来源控制
- **严格的数据来源要求**: 所有数据必须来自币安官方API
- **严格禁止**: 使用模拟、生成或第三方数据
- **绝对禁止非官方数据**: 严禁使用任何非官方API数据，包括基于现货数据推断的期货合约数据
- **官方API验证**: 必须验证数据来源为币安官方API端点
- **数据来源白名单**: 建立数据来源白名单机制
- **自动验证**: 实施数据来源自动验证

### 数据完整性标准
- **交易对覆盖率**: 必须获取≥80%的币安杠杆USDT交易对
- **数据质量分布**: A级数据≥60%，A+B级数据≥80%，总数据≥90%
- **核心币种保证**: 主流币种(前50名)必须100%获取且为A级数据
- **新币种包容**: 新上线币种即使数据少也要保存，标记为C/D级
- **数据真实性**: 所有数据必须来自官方API，严禁模拟数据
- **指纹验证**: 实施数据指纹验证，确保数据未被篡改

### 违规处理机制
- **立即停止**: 发现虚假数据立即停止系统运行
- **自动检测**: 建立自动检测和告警机制
- **质量门禁**: 实施数据质量门禁控制
- **安全审计**: 建立数据安全审计机制
- **非官方数据零容忍**: 发现任何非官方数据（包括推断、模拟、生成的数据）立即停止并删除

### 数据分级管理策略
- **A级数据**: 历史数据≥365天，完整性高，用于主要分析
- **B级数据**: 历史数据30-364天，质量良好，用于辅助分析
- **C级数据**: 历史数据7-29天，新币种数据，用于趋势观察
- **D级数据**: 历史数据1-6天，极新币种，仅作参考
- **保存原则**: 所有级别数据都保存，但使用时区别对待

## 🔧 终端和进程管理规范
### 终端使用原则
- **终端复用原则**: 优先在当前终端执行新任务，如无必要不新建终端
- **资源优化策略**: 避免同时运行多个资源密集型任务
- **进程生命周期管理**: 及时清理不需要的长时间运行进程
- **内存监控**: 监控内存使用情况，防止资源耗尽
- **任务调度**: 优先使用当前终端，减少系统开销

### 进程管理策略
- **进程清理**: 定期清理已完成或失败的进程
- **资源监控**: 监控终端数量和系统资源使用
- **任务排队**: 避免并发执行资源密集型任务
- **终端状态管理**: 使用 `list-processes` 定期检查进程状态

## 📱 终端输出监控规范（强制执行）
### 检查要求
- **强制要求**: 查看终端输出时必须检查所有终端
- **检查流程**:
  1. 使用 `list-processes` 查看所有终端状态
  2. 逐一检查运行中的终端: `read-process terminal_id`
  3. 检查最近完成的重要终端输出
  4. 识别异常终端（无输出、卡死、错误）

### 工具使用规范
- **优先使用 read-terminal**: 能够读取程序的实时输出，包括正在运行的程序
- **read-process 局限性**: 无法读取正在运行程序的实时输出，仅适用于已完成的程序
- **多工具结合**: 同时使用终端监控、日志文件、进程管理工具
- **及时问题发现**: 通过实时监控及时发现网络、API、性能等问题

### 问题识别和处理
- **空输出识别**: `<o></o>` 表示程序可能卡死
- **长时间运行**: 无输出需要检查
- **Python环境**: 执行环境问题需要诊断
- **处理措施**: 对卡死进程使用 `kill-process`
- **问题记录**: 记录异常终端的问题模式
- **健康检查**: 建立终端健康检查机制

### 常见问题诊断
- **Python环境问题**: 检查依赖包安装
- **网络连接问题**: 验证API访问能力
- **权限问题**: 检查文件读写权限
- **内存/资源问题**: 监控系统资源使用

### 最佳实践
- **执行后检查**: 每次执行命令后检查输出
- **超时设置**: 长时间运行的任务设置超时
- **日志保存**: 保存重要终端输出到日志
- **状态追踪**: 建立终端执行状态追踪表

## 🛠️ 开发工具和监控标准
### 实时监控工具
- **终端输出监控**: 使用 `read-terminal` 工具监控程序实时输出
- **进程状态管理**: 使用 `list-processes` 和 `read-process` 工具管理后台进程
- **日志文件监控**: 同时监控日志文件和终端输出，确保问题及时发现
- **输出缓冲处理**: 程序设计时考虑输出缓冲问题，使用 `sys.stdout.flush()` 确保实时显示

### 多层监控机制
- **终端输出**: 实时程序执行状态
- **日志文件**: 详细的执行记录和错误信息
- **结果文件**: 程序执行结果和统计数据
- **系统状态**: 内存、CPU、网络使用情况

### 问题诊断流程
- **首先使用 read-terminal**: 查看最新输出
- **检查日志文件**: 了解详细错误信息
- **分析外部因素**: 网络连接、API限制等外部因素
- **制定解决方案**: 根据问题类型制定相应解决方案

## ⚡ 系统性能要求
### 硬件适配
- **硬件适配**: 针对低配置笔记本电脑优化
- **内存管理**: 分批处理大数据集，避免内存溢出
- **存储优化**: 使用压缩格式，支持增量更新
- **并发控制**: 合理使用多线程，避免API限制

### 性能优化策略
- **内存优化**: 针对低配置硬件的内存管理
- **计算优化**: 高效的算法和数据结构
- **并发优化**: 合理的多线程和异步处理
- **缓存策略**: 智能的数据缓存机制

## 🔄 运行环境规范
### 程序执行规范
- **终端处理**: 程序运行完成后自动退出，无需手动确认
- **输出处理**: 等待程序完全执行完毕后再读取结果
- **错误处理**: 完善的异常捕获和错误恢复机制
- **日志记录**: 详细的操作日志和错误追踪

### 错误处理体系
- **异常捕获**: 全面的异常捕获和处理
- **错误恢复**: 自动错误恢复机制
- **日志系统**: 详细的操作日志和错误追踪
- **监控告警**: 系统状态监控和异常告警

## 🔍 问题解决策略
### 解决方法优先级
- **网络搜索**: 遇到问题时搜索最佳实践和解决方案
- **文档优先**: 优先查阅官方文档和技术规范
- **社区支持**: 利用开源社区资源和经验分享
- **迭代改进**: 持续优化和改进技术方案

### 网络连接解决方案
- **中国大陆优化**: 专门的网络连接解决方案
- **请求配置**: 自定义User-Agent和请求头
- **重试机制**: 自动重试和错误恢复
- **代理支持**: 支持多种代理配置
- **连接池**: HTTP连接池优化

## 📋 实时监控和调试策略
### 监控策略
- **实时监控策略**: 优先使用 read-terminal 能够读取程序的实时输出，包括正在运行的程序
- **read-process 局限性**: 无法读取正在运行程序的实时输出，仅适用于已完成的程序
- **多工具结合**: 同时使用终端监控、日志文件、进程管理工具
- **及时问题发现**: 通过实时监控及时发现网络、API、性能等问题

### 监控检查清单
- **程序启动**: 确认程序正常启动并开始执行
- **进度跟踪**: 监控处理进度和预计完成时间
- **错误识别**: 及时发现网络超时、API限制、数据质量等问题
- **性能监控**: 观察内存使用、处理速度、资源消耗
- **完成确认**: 确认程序正常完成并生成预期结果

### 问题处理流程
- **实时发现**: 通过 `read-terminal` 实时发现问题
- **快速诊断**: 结合日志文件分析问题根因
- **及时干预**: 必要时使用 `kill-process` 停止有问题的程序
- **方案调整**: 根据问题类型调整程序参数或网络配置
- **重新执行**: 优化后重新启动程序并继续监控

## 🔒 质量控制和验证流程
### 数据质量标准
- **数据完整性要求**: 数据完整性低于80%时系统拒绝继续执行
- **自动完整性检查**: 实施自动完整性检查机制
- **数据质量评分**: 建立数据质量评分体系
- **定期健康检查**: 定期进行数据健康检查

### 元数据管理
- **完整元数据**: 每个数据文件必须包含完整元数据
- **数据来源记录**: 记录数据来源、获取时间、验证方法
- **版本控制**: 建立数据版本控制机制
- **变更追踪**: 实施数据变更追踪

### 开发质量标准
- **代码审查**: 实施代码审查机制
- **单元测试**: 建立单元测试覆盖率要求
- **执行流程控制**: 严格按照模块顺序执行开发，关键步骤不得跳跃或并行执行
- **里程碑检查**: 每个模块完成后进行质量验证，建立里程碑检查点机制

### 风险控制机制
- **自动检测系统**: 实施自动检测非真实数据的机制
- **违规停止**: 发现违规数据立即停止系统运行
- **异常告警**: 建立异常数据告警机制
- **安全防护**: 实施数据安全防护措施

## 🧪 测试和验证规范
### 单元测试体系
- **核心功能**: 所有核心功能的单元测试
- **边界测试**: 边界条件和异常情况测试
- **性能测试**: 关键函数的性能测试
- **覆盖率要求**: 90%以上的测试覆盖率
- **实时监控**: 使用 `read-terminal` 监控测试执行过程

### 集成测试策略
- **模块协作**: 验证模块间的协作功能
- **数据流**: 验证数据在模块间的流转
- **端到端**: 完整流程的端到端测试
- **回归测试**: 确保修改不影响现有功能

### 性能测试规范
- **低配置测试**: 在低配置硬件上的性能测试
- **压力测试**: 大数据量和高并发测试
- **稳定性测试**: 长时间运行的稳定性验证
- **资源监控**: 内存、CPU、磁盘使用监控

### 数据验证体系
- **真实性验证**: 确保所有数据来源真实可靠
- **完整性验证**: 验证数据的完整性和一致性
- **准确性验证**: 验证数据的准确性和时效性
- **可追溯性**: 建立完整的数据追溯体系

## 📦 包管理规范
### 包管理器使用
- **使用包管理器**: 始终使用适当的包管理器进行依赖管理，而不是手动编辑包配置文件
- **正确的包管理器命令**:
  - **JavaScript/Node.js**: `npm install`, `npm uninstall`, `yarn add`, `yarn remove`, `pnpm add/remove`
  - **Python**: `pip install`, `pip uninstall`, `poetry add`, `poetry remove`, `conda install/remove`
  - **Rust**: `cargo add`, `cargo remove` (Cargo 1.62+)
  - **Go**: `go get`, `go mod tidy`
  - **Ruby**: `gem install`, `bundle add`, `bundle remove`
  - **PHP**: `composer require`, `composer remove`
  - **C#/.NET**: `dotnet add package`, `dotnet remove package`
  - **Java**: Maven (`mvn dependency:add`) 或 Gradle 命令

### 包管理原则
- **自动解析**: 包管理器自动解析正确版本，处理依赖冲突，更新锁文件，维护环境一致性
- **避免手动编辑**: 手动编辑包文件经常导致版本不匹配、依赖冲突和构建失败
- **例外情况**: 只有在进行复杂配置变更时才直接编辑包文件（如自定义脚本、构建配置或仓库设置）

## 🎯 项目执行原则
### 执行流程控制
- **严格按计划执行**: 严格按照模块顺序执行开发
- **质量门禁**: 每个模块完成后进行质量验证
- **里程碑检查**: 建立里程碑检查点机制
- **持续改进**: 基于反馈持续优化改进

### 模块化开发原则
- **模块化设计**: 松耦合的模块化架构
- **接口标准**: 统一的模块间接口规范
- **配置管理**: 集中化的配置管理系统
- **依赖管理**: 清晰的模块依赖关系

## 🔄 防未来函数核心策略（严格执行）
### 时间严格性要求
- **训练数据时间**: 训练数据必须严格早于验证数据
- **特征时间对齐**: 所有特征必须基于T日或之前的数据
- **目标变量时间**: 目标变量必须是T+2日的真实数据
- **滚动训练**: 使用滚动时间窗口，模拟真实交易环境
- **数据切分原则**: 按时间顺序切分，禁止随机切分

### 特征计算时点控制
- **特征计算时点**: 确保所有技术指标基于历史数据计算
- **预测时点控制**: 严格控制预测发生的时间点
- **时间戳检查**: 验证所有数据的时间戳正确性
- **特征时间验证**: 确保特征计算不使用未来数据
- **目标变量验证**: 确保目标变量时间正确

### 时间窗口设计原则
```
防未来函数时间线：
T-n ~ T-1: 历史数据用于特征计算
T: 当前时点，特征计算截止时间
T+1: 数据处理和模型预测时间
T+2: 目标预测日期，执行交易
```

### 数据使用规范
- **特征数据**: 只能使用T日及之前的数据
- **技术指标**: 基于T日收盘价及之前计算
- **目标变量**: 使用T+2日的真实价格数据
- **验证数据**: 严格按时间顺序分割
- **测试环境**: 模拟真实交易时间约束

### 防过拟合策略
- **早停机制**: 基于验证集的早停
- **正则化**: L1/L2正则化参数调优
- **特征随机**: 随机特征选择训练
- **数据增强**: 时间序列数据增强技术
- **时间间隔**: 训练和验证之间设置时间间隔

## 📊 数据获取和存储规范
### 交易对管理
- **官方交易对获取**: 从币安官方API获取390-410个杠杆USDT交易对
- **筛选条件**: status='TRADING' + quoteAsset='USDT' + isMarginTradingAllowed=True
- **更新频率**: 支持实时获取最新交易对列表
- **验证机制**: 通过实际数据下载验证交易对有效性

### 历史数据获取策略
- **数据类型**: 日K线数据 (OHLCV格式)
- **时间范围**: 从各交易对上线开始到最新
- **获取方式**: 批量并发获取，支持断点续传
- **限流控制**: 遵守API调用频率限制
- **错误处理**: 完善的重试和恢复机制
- **包容性原则**: 保存所有可获取的数据，不设最小数据量阈值

### 数据存储架构
- **主格式**: Parquet压缩格式存储
- **元数据**: JSON格式记录数据来源和质量信息
- **目录结构**: 按交易对分类存储
- **版本控制**: 支持数据版本管理
- **增量更新**: 支持增量数据更新和同步

### 数据质量保证
- **完整性检查**: 验证数据时间连续性
- **逻辑验证**: 检查OHLCV数据逻辑正确性
- **异常检测**: 识别和处理异常数据点
- **质量评分**: 建立数据质量评分机制
- **自动修复**: 自动修复可恢复的数据问题

## 🤖 模型训练和预测规范
### 训练验证框架（Kaggle Embargo CV技术）
- **7折交叉验证**: 按时间序列分割为7个fold
- **Embargo机制**: 训练测试集间隔7天防止数据泄露
- **时间戳排序**: 严格按时间戳顺序分割数据
- **禁运期设置**: embargo = 7天 (对应原文3750分钟)
- **前向验证**: 训练集必须早于测试集

### 模型训练机制
- **增量训练**: 每日增量更新模型
- **全量重训**: 定期全量重新训练
- **在线学习**: 实时学习市场变化
- **迁移学习**: 跨币种知识迁移
- **T+2预测训练**: 专门针对后天(T+2)涨跌幅的预测模型训练

### 预测时间窗口设计
- **核心预测**: 预测后天（T+2日）涨跌幅
- **预测逻辑**: 1月1日结束后预测1月3日涨跌幅
- **交易执行**: 1月3日根据1月1日的预测结果执行交易
- **时间缓冲**: 考虑币圈24小时交易特性，预留预测和交易执行时间
- **实际应用**: T日收盘后预测T+2日涨跌，T+2日开盘时执行交易

### 防未来函数训练流程
```
严格时间序列训练流程：
1. 数据准备阶段 (T日23:59前)
   - 收集T日及之前的所有历史数据
   - 计算基于T日的技术指标
   - 准备T+2日的目标变量

2. 模型训练阶段 (T+1日00:00-02:00)
   - 使用滚动时间窗口训练
   - 严格按时间顺序分割数据
   - 验证集必须晚于训练集

3. 预测生成阶段 (T+1日02:00-04:00)
   - 基于T日特征预测T+2日涨跌幅
   - 生成交易信号和置信度
   - 保存预测结果和元数据

4. 交易执行阶段 (T+2日开盘)
   - 根据预测结果执行交易
   - 记录实际执行价格
   - 为后续模型评估准备数据
```

## 💹 交易策略规范
### 策略核心逻辑
- **信号生成机制**: 基于多模型预测的投票机制
- **概率阈值**: 设置买入/卖出概率阈值
- **信号过滤**: 多重条件过滤虚假信号
- **时间窗口**: 不同时间尺度信号综合

### 仓位管理系统
- **动态仓位**: 基于市场波动调整仓位
- **风险预算**: 单笔交易风险控制
- **资金管理**: 总资金的分配策略
- **杠杆控制**: 杠杆倍数动态调整

### 风险控制机制
- **止损策略**: 固定止损和动态止损
- **止盈策略**: 分批止盈和移动止盈
- **最大回撤**: 回撤控制和资金保护
- **相关性控制**: 避免过度集中风险

### 参数自适应
- **市场状态识别**: 牛市、熊市、震荡市识别
- **参数动态调整**: 根据市场状态调整参数
- **性能监控**: 实时监控策略性能
- **自动优化**: 基于历史表现自动优化

## 📈 回测和性能评估规范
### 回测框架设计
- **事件驱动**: 基于事件的回测引擎
- **滑点模拟**: 真实的交易成本模拟
- **延迟模拟**: 交易执行延迟模拟
- **流动性考虑**: 市场流动性对交易的影响

### 性能评估体系
- **收益指标**: 总收益率、年化收益率、超额收益
- **风险指标**: 夏普比率、索提诺比率、卡尔马比率
- **回撤指标**: 最大回撤、平均回撤、回撤持续时间
- **稳定性指标**: 胜率、盈亏比、连续亏损次数

### 风险管理验证
- **压力测试**: 极端市场条件下的表现
- **敏感性分析**: 参数变化对结果的影响
- **蒙特卡洛**: 随机模拟验证策略稳健性
- **情景分析**: 不同市场情景下的表现

## 🎯 项目交付标准
### 功能交付标准
- **系统功能要求**: 所有核心模块功能正常运行
- **系统集成测试**: 全部通过
- **用户界面**: 友好易用
- **错误处理**: 机制完善

### 性能交付标准
- **硬件兼容**: 在低配置硬件上稳定运行
- **内存控制**: 使用控制在合理范围
- **处理速度**: 满足实时要求
- **响应时间**: 在可接受范围

### 质量交付标准
- **真实数据**: 100%使用真实市场数据
- **测试覆盖**: 核心功能测试覆盖率≥90%
- **代码质量**: 符合规范要求
- **文档完整**: 性和准确性验证

### 验收标准
- **技术验收**: 功能测试、性能测试、安全测试、兼容性测试全部通过
- **业务验收**: 回测结果合理可信，策略逻辑清晰正确，风险控制机制有效，用户体验满足要求

## 📊 特征工程规范（基于Kaggle第三名方案）
### 核心特征工程策略
- **多时间周期比率特征**: [60, 300, 900] 分钟对应 [5, 10, 20] 日级别
- **F1特征**: 当前收盘价 / 过去N天平均收盘价的对数比率
- **F2特征**: 当前收盘价 / N天前收盘价的对数比率
- **市场平均特征**: 所有398个币种F1、F2的加权平均
- **残差特征**: 个币种F1、F2与市场平均的差值
- **相关性特征**: 利用币种间相关性提升预测精度

### 内存优化和数据处理
- **reduce_mem_usage函数**: 自动优化DataFrame内存占用
- **数据类型优化**: float64→float32, int64→int32
- **分批处理**: 避免内存溢出，支持大规模数据
- **前向填充**: 限制填充数量，避免过度插值
- **数据合并策略**: 多币种数据按时间戳合并

### 目标变量设计（防未来函数）
- **15分钟对数收益率**: 适配到日级别T+2预测
- **市场相关性调整**: 减去市场平均影响
- **3750分钟滚动窗口**: 对应7天Embargo机制
- **Beta系数计算**: 个币种与市场的相关性系数

### 传统技术指标体系
- **趋势指标**: MA5, MA10, MA20, MA50, MA200, EMA12, EMA26, EMA50, 布林带, 抛物线SAR
- **动量指标**: RSI (14日、30日), MACD (快线、慢线、柱状图), 随机指标 (%K、%D值), 威廉指标 (%R值)
- **波动率指标**: ATR (真实波动幅度), 标准差, 历史波动率, VIX类指标
- **成交量指标**: OBV、成交量比率, 量价关系, 资金流向, 换手率

### 市场特征构建
- **价格特征**: 日收益率、周收益率、月收益率, 相对高低位置指标, 跳空缺口识别, 关键价位识别
- **T+2预测特征**: 专门为后天(T+2)预测设计的价格特征
- **防未来函数特征设计**: 所有特征必须基于T日及之前的数据, 使用滞后特征避免数据泄露
- **市场情绪特征**: 多空比例, 恐贪指数, 相关性分析, 市场强度

### 数据预处理
- **数据清洗**: 识别和处理极端值, 合理的缺失值处理策略, 噪声数据平滑处理, 确保所有特征时间一致
- **特征标准化**: Z-score标准化, Min-Max归一化, 基于中位数的稳健标准化, 非线性变换处理
- **特征选择**: 相关性分析去除高相关特征, 基于模型的特征重要性, 递归特征消除, 主成分分析降维

### 分级数据特征工程
- **A级数据**: 计算所有技术指标，包括长期趋势指标
- **B级数据**: 计算中短期技术指标，适当调整参数
- **C级数据**: 计算短期指标，重点关注价格动量
- **D级数据**: 计算基础指标，主要用于趋势识别
- **自适应参数**: 根据数据长度自动调整指标参数

### T+2预测目标变量
- **后天涨跌幅**: 创建T+2日的价格变化率作为目标变量
- **后天方向**: 创建T+2日的价格方向（上涨/下跌）作为分类目标
- **后天超额收益**: 相对于市场基准的T+2日超额收益
- **后天风险度**: T+2日预期波动率和风险评估
- **时间窗口设计**: 确保特征和目标变量的时间窗口正确对齐

### 新币种特征工程
- **快速指标**: 优先计算快速响应的技术指标
- **相对指标**: 使用相对价格变化而非绝对值
- **市场对比**: 与主流币种进行相对强度分析
- **趋势捕捉**: 重点捕捉短期趋势和动量信号
- **风险标记**: 为新币种数据添加风险等级标记

### 质量验证
- **特征验证**: 验证所有技术指标计算, 确保特征数据时间一致性, 验证特征数据完整性, 检查特征值合理性范围
- **防未来函数验证**: 验证所有数据的时间戳正确性, 确保特征计算不使用未来数据, 确保目标变量时间正确, 自动检测潜在的数据泄露

## 🐳 Docker容器化规范
### 容器化设计原则
- **微服务架构**: 每个功能模块独立容器化
- **环境一致性**: 开发、测试、生产环境完全一致
- **可扩展性**: 支持水平扩展和负载均衡
- **资源隔离**: 容器间资源隔离，避免相互影响
- **版本管理**: 容器镜像版本化管理

### Docker服务架构
- **数据获取服务**: crypto-quant/data-fetcher:latest
- **Qlib数据引擎服务**: crypto-quant/qlib-engine:latest
- **特征工程服务**: crypto-quant/feature-engine:latest
- **模型训练服务**: crypto-quant/model-trainer:latest
- **回测引擎服务**: crypto-quant/backtest-engine:latest
- **Web GUI服务**: crypto-quant/web-gui:latest
- **Redis缓存服务**: redis:alpine
- **PostgreSQL数据库服务**: postgres:13

### 容器化环境标准
- **Docker版本**: Docker 20.10+ 和 Docker Compose 2.0+
- **基础镜像**: Python 3.9-slim 官方镜像
- **Qlib版本**: Qlib 0.9.0+ 稳定版本
- **容器编排**: Docker Compose管理多服务架构
- **环境变量**: 统一的环境变量管理

## 🖥️ GUI系统规范
### GUI系统架构
- **主程序**: `enhanced_crypto_gui.py` - 增强版GUI应用程序
- **配置管理**: `gui_config.py` - GUI配置和样式管理
- **基础版本**: `crypto_gui_app.py` - 基础GUI实现
- **测试程序**: `test_gui.py` - GUI功能测试

### 功能模块集成
- **数据管理模块**: 获取官方交易对, 检查数据状态, 获取历史数据, 验证数据质量
- **特征工程模块**: 技术指标计算和Kaggle特征工程
- **模型训练模块**: LightGBM/XGBoost模型训练
- **回测分析模块**: 策略回测和性能分析
- **交易策略模块**: MACD等技术指标策略
- **分析工具模块**: 数据分析和可视化

### GUI用户体验
- **实时日志**: 完整的日志系统和进度显示
- **状态监控**: 实时系统状态检查和显示
- **任务控制**: 后台任务执行、停止和进度跟踪
- **配置管理**: 模型类型、策略类型等参数配置
- **快捷操作**: 快捷键支持和工具提示

### exe打包部署
- **打包配置**: `crypto_gui.spec` - PyInstaller配置文件
- **构建脚本**: `build_gui.bat` - 一键构建脚本
- **安装脚本**: `install_gui.bat` - 一键安装脚本
- **部署工具**: `deploy_gui.py` - 自动化部署脚本
- **输出文件**: `dist/CryptoQuantGUI.exe` - 独立可执行文件

## 📚 文档和部署规范
### 技术文档体系
- **系统文档**: 详细的系统架构设计文档, 完整的API接口文档, 数据存储结构设计文档, 核心算法的详细说明
- **用户文档**: 系统安装和环境配置指南, 详细的操作使用手册, 参数配置和调优指南, 常见问题和解决方案
- **开发文档**: 代码编写和注释规范, 模块开发和扩展指南, 测试编写和执行指南, 生产环境部署指南

### 部署和维护
- **部署策略**: 生产环境的准备和配置, 系统依赖的安装和管理, 系统配置的部署和验证, 生产环境的性能调优
- **维护体系**: 系统运行状态监控, 日志收集、分析和管理, 数据和配置的备份策略, 系统更新和升级机制

## 🚀 深度学习集成规范（已完成验证）
### BiAGRU模型架构
- **序列长度**: 24小时输入序列长度
- **预测时间**: 预测未来12小时收益率
- **隐藏层**: 64个隐藏单元的GRU
- **注意力机制**: additive注意力类型
- **损失函数**: IC损失函数优化相关性

### 深度学习特征工程
- **时间序列特征**: 价格和成交量的一阶变化率
- **标准化处理**: 时间和币种两个维度标准化
- **序列构造**: 24小时滑动窗口，预测未来12小时
- **避免数据泄露**: 严格的时间窗口控制

### 因子评估体系
- **IC指标**: 信息系数(Spearman相关系数)
- **RankIC**: 排序后的Pearson相关系数
- **ICIR**: IC信息比率 = mean(IC) / std(IC)
- **分组回测**: 10分组和20分组策略验证

### 投资组合构造
- **分组策略**: 10分组/20分组，做多第1组，做空第N组
- **权重方法**: 等权重和排序加权
- **风险控制**: 多空各50%配置，交易成本计算
- **性能评估**: 年化收益、夏普比率、最大回撤

### 深度学习与传统方法集成
- **模型融合策略**: 深度学习因子, 传统技术指标, Kaggle特征, 集成预测
- **时间窗口协调**: 24小时序列预测12小时收益, 日级别技术指标, T+2日预测目标, 统一框架

## 🎯 成功关键因素
### 技术关键因素
- **数据质量**: 确保100%真实数据来源
- **网络连接**: 稳定的API连接解决方案
- **性能优化**: 适配低配置硬件要求
- **模块化设计**: 清晰的架构和接口设计

### 管理关键因素
- **严格执行**: 按照计划顺序严格执行
- **质量控制**: 每个阶段的质量门禁检查
- **风险管控**: 及时识别和处理项目风险
- **持续改进**: 基于反馈持续优化改进

## 📋 项目状态管理规范
### 项目状态更新要求
- **定期更新**: 定期更新@`c:\quant/项目状态.md`文件
- **状态跟踪**: 实时跟踪各模块完成状态和进度
- **成果记录**: 详细记录系统性能表现和技术成就
- **问题追踪**: 记录遇到的问题和解决方案
- **里程碑管理**: 标记重要的项目里程碑和完成节点

### 状态报告内容要求
- **项目概述**: 包含项目名称、技术栈、数据源、目标市场
- **模块状态**: 详细的各模块完成状态和完成度
- **性能表现**: 系统回测结果和关键性能指标
- **技术架构**: 项目文件结构和技术特点
- **项目价值**: 学术价值、实用价值、技术价值
- **下一步计划**: 短期、中期、长期发展规划

### 更新触发条件
- **重大功能完成**: 主要模块或功能完成时
- **性能突破**: 系统性能有重大提升时
- **技术创新**: 集成新技术或方法时
- **问题解决**: 解决重要技术问题时
- **定期维护**: 至少每周更新一次项目状态

---

**规则文档版本**: v2.0 (详细规则版)
**最后更新**: 2025年1月19日
**规则来源**: 从项目计划.md提取的详细规则
**适用范围**: 加密货币量化交易系统开发的所有阶段
**执行级别**: 强制执行 (always_apply)