{"download_summary": {"total_contracts": 504, "successful_downloads": 291, "failed_downloads": 0, "skipped_downloads": 0, "total_data_points": 99104, "success_rate": 57.738095238095234, "duration_seconds": 842.852726, "duration_formatted": "0:14:02.852726"}, "data_source_verification": {"api_endpoint": "https://fapi.binance.com/fapi/v1/klines", "data_source": "OFFICIAL_BINANCE_FUTURES_API", "authenticity": "VERIFIED_OFFICIAL_DATA", "no_simulated_data": true, "no_generated_data": true}, "progress_details": {"last_update": "2025-07-29T11:32:10.873778", "current_symbol": "ZROUSDT", "completed_count": 504, "failed_count": 0, "skipped_count": 0, "total_count": 504, "completion_rate": 100.0, "completed_symbols": ["BTCUSDT", "ETHUSDT", "BCHUSDT", "XRPUSDT", "LTCUSDT", "TRXUSDT", "ETCUSDT", "LINKUSDT", "XLMUSDT", "ADAUSDT", "XMRUSDT", "DASHUSDT", "ZECUSDT", "XTZUSDT", "BNBUSDT", "ATOMUSDT", "ONTUSDT", "IOTAUSDT", "BATUSDT", "VETUSDT", "NEOUSDT", "QTUMUSDT", "IOSTUSDT", "THETAUSDT", "ALGOUSDT", "ZILUSDT", "KNCUSDT", "ZRXUSDT", "COMPUSDT", "DOGEUSDT", "SXPUSDT", "KAVAUSDT", "BANDUSDT", "RLCUSDT", "MKRUSDT", "SNXUSDT", "DOTUSDT", "DEFIUSDT", "YFIUSDT", "CRVUSDT", "TRBUSDT", "RUNEUSDT", "SUSHIUSDT", "EGLDUSDT", "SOLUSDT", "ICXUSDT", "STORJUSDT", "UNIUSDT", "AVAXUSDT", "ENJUSDT", "FLMUSDT", "KSMUSDT", "NEARUSDT", "AAVEUSDT", "FILUSDT", "RSRUSDT", "LRCUSDT", "BELUSDT", "AXSUSDT", "ALPHAUSDT", "ZENUSDT", "SKLUSDT", "GRTUSDT", "1INCHUSDT", "SANDUSDT", "CHZUSDT", "ANKRUSDT", "RVNUSDT", "SFPUSDT", "COTIUSDT", "CHRUSDT", "MANAUSDT", "ALICEUSDT", "GTCUSDT", "HBARUSDT", "ONEUSDT", "DENTUSDT", "CELRUSDT", "HOTUSDT", "MTLUSDT", "OGNUSDT", "NKNUSDT", "1000SHIBUSDT", "BAKEUSDT", "BTCDOMUSDT", "MASKUSDT", "ICPUSDT", "IOTXUSDT", "C98USDT", "ATAUSDT", "DYDXUSDT", "1000XECUSDT", "GALAUSDT", "CELOUSDT", "ARUSDT", "ARPAUSDT", "CTSIUSDT", "LPTUSDT", "ENSUSDT", "PEOPLEUSDT", "ROSEUSDT", "DUSKUSDT", "FLOWUSDT", "IMXUSDT", "API3USDT", "GMTUSDT", "APEUSDT", "WOOUSDT", "JASMYUSDT", "OPUSDT", "INJUSDT", "STGUSDT", "SPELLUSDT", "1000LUNCUSDT", "LUNA2USDT", "LDOUSDT", "APTUSDT", "QNTUSDT", "FETUSDT", "FXSUSDT", "HOOKUSDT", "MAGICUSDT", "TUSDT", "HIGHUSDT", "MINAUSDT", "ASTRUSDT", "PHBUSDT", "GMXUSDT", "CFXUSDT", "STXUSDT", "ACHUSDT", "SSVUSDT", "CKBUSDT", "PERPUSDT", "TRUUSDT", "LQTYUSDT", "USDCUSDT", "IDUSDT", "ARBUSDT", "JOEUSDT", "TLMUSDT", "LEVERUSDT", "RDNTUSDT", "HFTUSDT", "XVSUSDT", "BLURUSDT", "EDUUSDT", "SUIUSDT", "1000PEPEUSDT", "1000FLOKIUSDT", "UMAUSDT", "NMRUSDT", "MAVUSDT", "XVGUSDT", "WLDUSDT", "PENDLEUSDT", "ARKMUSDT", "AGLDUSDT", "YGGUSDT", "DODOXUSDT", "BNTUSDT", "OXTUSDT", "SEIUSDT", "CYBERUSDT", "HIFIUSDT", "ARKUSDT", "BICOUSDT", "BIGTIMEUSDT", "WAXPUSDT", "BSVUSDT", "RIFUSDT", "POLYXUSDT", "GASUSDT", "POWRUSDT", "TIAUSDT", "CAKEUSDT", "MEMEUSDT", "TWTUSDT", "TOKENUSDT", "ORDIUSDT", "STEEMUSDT", "ILVUSDT", "NTRNUSDT", "KASUSDT", "BEAMXUSDT", "1000BONKUSDT", "PYTHUSDT", "SUPERUSDT", "USTCUSDT", "ONGUSDT", "ETHWUSDT", "JTOUSDT", "1000SATSUSDT", "AUCTIONUSDT", "1000RATSUSDT", "ACEUSDT", "MOVRUSDT", "NFPUSDT", "AIUSDT", "XAIUSDT", "MANTAUSDT", "WIFUSDT", "ONDOUSDT", "ALTUSDT", "LSKUSDT", "JUPUSDT", "ZETAUSDT", "RONINUSDT", "DYMUSDT", "OMUSDT", "PIXELUSDT", "STRKUSDT", "GLMUSDT", "1000000BOBUSDT", "1000000MOGUSDT", "1000CATUSDT", "1000CHEEMSUSDT", "1000WHYUSDT", "1000XUSDT", "1MBABYDOGEUSDT", "ACTUSDT", "ACXUSDT", "AERGOUSDT", "AEROUSDT", "AEVOUSDT", "AGIXUSDT", "AGTUSDT", "AI16ZUSDT", "AINUSDT", "AIOTUSDT", "AIXBTUSDT", "AKTUSDT", "ALCHUSDT", "ALPACAUSDT", "ALPINEUSDT", "AMBUSDT", "ANIMEUSDT", "ANTUSDT", "ARCUSDT", "ASRUSDT", "ATHUSDT", "AUDIOUSDT", "AUSDT", "AVAAIUSDT", "AVAUSDT", "AWEUSDT", "AXLUSDT", "B2USDT", "B3USDT", "BABYUSDT", "BADGERUSDT", "BALUSDT", "BANANAS31USDT", "BANANAUSDT", "BANKUSDT", "BANUSDT", "BBUSDT", "BDXNUSDT", "BERAUSDT", "BIDUSDT", "BIOUSDT", "BLUEBIRDUSDT", "BLZUSDT", "BMTUSDT", "BNXUSDT", "BOMEUSDT", "BONDUSDT", "BRETTUSDT", "BROCCOLI714USDT", "BROCCOLIF3BUSDT", "BRUSDT", "BSWUSDT", "BULLAUSDT", "BUSDT", "CATIUSDT", "CETUSUSDT", "CGPTUSDT", "CHESSUSDT", "CHILLGUYUSDT", "COMBOUSDT", "COOKIEUSDT", "COSUSDT", "COWUSDT", "CROSSUSDT", "CTKUSDT", "CUSDT", "CVCUSDT", "CVXUSDT", "DARUSDT", "DEEPUSDT", "DEGENUSDT", "DEGOUSDT", "DEXEUSDT", "DFUSDT", "DGBUSDT", "DIAUSDT", "DMCUSDT", "DOGSUSDT", "DOLOUSDT", "DOODUSDT", "DRIFTUSDT", "DUSDT", "EIGENUSDT", "ENAUSDT", "EOSUSDT", "EPICUSDT", "EPTUSDT", "ERAUSDT", "ETHFIUSDT", "FARTCOINUSDT", "FHEUSDT", "FIDAUSDT", "FIOUSDT", "FISUSDT", "FLUXUSDT", "FOOTBALLUSDT", "FORMUSDT", "FORTHUSDT", "FTMUSDT", "FTTUSDT", "FUNUSDT", "FUSDT", "GHSTUSDT", "GLMRUSDT", "GOATUSDT", "GPSUSDT", "GRASSUSDT", "GRIFFAINUSDT", "GUNUSDT", "GUSDT", "HAEDALUSDT", "HEIUSDT", "HIPPOUSDT", "HIVEUSDT", "HMSTRUSDT", "HOMEUSDT", "HUMAUSDT", "HUSDT", "HYPERUSDT", "HYPEUSDT", "ICNTUSDT", "IDEXUSDT", "IDOLUSDT", "INITUSDT", "IOUSDT", "IPUSDT", "JELLYJELLYUSDT", "JSTUSDT", "KAIAUSDT", "KAITOUSDT", "KDAUSDT", "KERNELUSDT", "KEYUSDT", "KLAYUSDT", "KMNOUSDT", "KOMAUSDT", "LAUSDT", "LAYERUSDT", "LINAUSDT", "LISTAUSDT", "LITUSDT", "LOKAUSDT", "LOOMUSDT", "LUMIAUSDT", "MAVIAUSDT", "MBLUSDT", "MBOXUSDT", "MDTUSDT", "MELANIAUSDT", "MEMEFIUSDT", "MERLUSDT", "METISUSDT", "MEUSDT", "MEWUSDT", "MILKUSDT", "MLNUSDT", "MOCAUSDT", "MOODENGUSDT", "MORPHOUSDT", "MOVEUSDT", "MUBARAKUSDT", "MUSDT", "MYROUSDT", "MYXUSDT", "NEIROETHUSDT", "NEIROUSDT", "NEWTUSDT", "NILUSDT", "NOTUSDT", "NULSUSDT", "NXPCUSDT", "OBOLUSDT", "OCEANUSDT", "OGUSDT", "OLUSDT", "OMGUSDT", "OMNIUSDT", "ORBSUSDT", "ORCAUSDT", "PARTIUSDT", "PAXGUSDT", "PENGUUSDT", "PHAUSDT", "PIPPINUSDT", "PLUMEUSDT", "PNUTUSDT", "POLUSDT", "PONKEUSDT", "POPCATUSDT", "PORT3USDT", "PORTALUSDT", "PROMPTUSDT", "PROMUSDT", "PUFFERUSDT", "PUMPBTCUSDT", "PUMPUSDT", "PUNDIXUSDT", "QUICKUSDT", "RADUSDT", "RAREUSDT", "RAYSOLUSDT", "RAYUSDT", "REDUSDT", "REEFUSDT", "REIUSDT", "RENDERUSDT", "RENUSDT", "RESOLVUSDT", "REZUSDT", "RPLUSDT", "SAFEUSDT", "SAGAUSDT", "SAHARAUSDT", "SANTOSUSDT", "SCRTUSDT", "SCRUSDT", "SCUSDT", "SHELLUSDT", "SIGNUSDT", "SIRENUSDT", "SKATEUSDT", "SKYAIUSDT", "SLERFUSDT", "SLPUSDT", "SNTUSDT", "SOLVUSDT", "SONICUSDT", "SOONUSDT", "SOPHUSDT", "SPKUSDT", "SPXUSDT", "SQDUSDT", "STMXUSDT", "STOUSDT", "STPTUSDT", "STRAXUSDT", "SUNUSDT", "SUSDT", "SWARMSUSDT", "SWELLUSDT", "SXTUSDT", "SYNUSDT", "SYRUPUSDT", "SYSUSDT", "TACUSDT", "TAGUSDT", "TAIKOUSDT", "TANSSIUSDT", "TAOUSDT", "TAUSDT", "THEUSDT", "TNSRUSDT", "TOMOUSDT", "TONUSDT", "TROYUSDT", "TRUMPUSDT", "TSTUSDT", "TURBOUSDT", "TUTUSDT", "UNFIUSDT", "USUALUSDT", "UXLINKUSDT", "VANAUSDT", "VANRYUSDT", "VELODROMEUSDT", "VELVETUSDT", "VICUSDT", "VIDTUSDT", "VINEUSDT", "VIRTUALUSDT", "VOXELUSDT", "VTHOUSDT", "VVVUSDT", "WALUSDT", "WAVESUSDT", "WCTUSDT", "WUSDT", "XCNUSDT", "XEMUSDT", "ZEREBROUSDT", "ZKJUSDT", "ZKUSDT", "ZORAUSDT", "ZROUSDT"], "failed_symbols": [], "skipped_symbols": [], "statistics": {"start_time": "2025-07-29T09:37:33.736399", "end_time": null, "total_contracts": 457, "successful_downloads": 213, "failed_downloads": 0, "skipped_downloads": 0, "total_data_points": 236639, "total_file_size": 17959003, "api_calls": 299, "errors": []}}, "timestamp": "2025-07-29T11:32:12.887608"}